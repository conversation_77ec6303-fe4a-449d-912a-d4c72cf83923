<template>
  <div class="app-container">
    <div class="header">
      <div class="progress-overview">
        <div class="progress-stats">
          <div class="progress-count">
            <span class="current">{{ completedTasks }}</span>
            <span class="separator">/</span>
            <span class="total">{{ totalTasks }}</span>
          </div>
          <div class="progress-label">任务进度</div>
        </div>
        <div class="time-stats">
          <div class="total-time">副本已进行: {{ totalTimeSpent }}</div>
          <div class="deadline">截止: {{ deadline }}</div>
        </div>
      </div>
    </div>
    
    <div class="path-board">
      <div class="svg-container"
           @mousedown="startDrag"
           @mousemove="onDrag"
           @mouseup="endDrag"
           @mouseleave="endDrag"
           @touchstart="startDrag"
           @touchmove="onDrag"
           @touchend="endDrag">
        <div class="svg-viewport">
          <svg :width="canvasWidth" :height="canvasHeight" ref="svgRef"
               :style="{ transform: `translate(${dragOffset.x}px, ${dragOffset.y}px)` }">
          <!-- 底层：灰色完整路径 -->
          <path
            v-if="pathD"
            :d="pathD"
            stroke="#ccc"
            stroke-width="3"
            fill="none"
            stroke-linecap="round"
          />

          <!-- 中间层：紫色路径，通过clip-path控制显示区域 -->
          <g :style="{ clipPath: `inset(0 ${clipRightPercent}% 0 0)` }">
            <path
              v-if="pathD"
              :d="pathD"
              ref="progressPath"
              stroke="#9b5de5"
              stroke-width="3"
              fill="none"
              stroke-linecap="round"
            />
          </g>

          <!-- 节点 -->
          <g v-for="(pt, idx) in pathPoints" :key="'node-'+idx">
            <circle
              :cx="pt[0]"
              :cy="pt[1]"
              :r="getNodeRadius(idx)"
              :fill="getNodeColor(idx)"
              stroke="#333"
              stroke-width="1"
              @mouseover="hoverIndex = idx"
              @mouseout="hoverIndex = null"
              @click="editSubtask(nodes[idx], idx)"
              class="node-circle"
              :class="{
                'node-pending': nodeStates[idx] === NODE_STATES.PENDING,
                'node-in-progress': nodeStates[idx] === NODE_STATES.IN_PROGRESS,
                'node-completed': nodeStates[idx] === NODE_STATES.COMPLETED
              }"
            />
            <text
              :x="pt[0]"
              :y="pt[1] - 15"
              class="node-label"
              text-anchor="middle"
            >{{ idx + 1 }}</text>
          </g>
          </svg>
        </div>
      </div>
    </div>
    
    <div class="status-bar">
      <div class="status-item">
        <div class="status-dot completed-dot"></div>
        <span>已完成</span>
      </div>
      <div class="status-item">
        <div class="status-dot inprogress-dot"></div>
        <span>进行中</span>
      </div>
      <div class="status-item">
        <div class="status-dot notstarted-dot"></div>
        <span>未开始</span>
      </div>
    </div>
    
    <div class="detail-board">
      <div class="detail-header">
        <div class="detail-title">当前任务详情</div>
        <button class="record-btn" @click="openRecordPopup">
          <i class="fas fa-book"></i> 记录
        </button>
      </div>
      
      <div class="detail-block">
        <div class="block-title">
          <i class="fas fa-tasks"></i> 任务内容
        </div>
        <div class="block-content">
          完成React组件开发实践，包括状态管理和生命周期应用
        </div>
      </div>
      
      <div class="detail-block">
        <div class="block-title">
          <i class="fas fa-flag-checkered"></i> 完成标准
        </div>
        <div class="block-content">
          <div class="completion-criteria">
            <label class="checkbox-item">
              <input type="checkbox" v-model="criteria.component" />
              <span class="checkmark"></span>
              <span class="criteria-text">实现至少3个功能组件</span>
            </label>
            <label class="checkbox-item">
              <input type="checkbox" v-model="criteria.contextApi" />
              <span class="checkmark"></span>
              <span class="criteria-text">使用Context API进行状态管理</span>
            </label>
            <label class="checkbox-item">
              <input type="checkbox" v-model="criteria.testing" />
              <span class="checkmark"></span>
              <span class="criteria-text">通过所有单元测试</span>
            </label>
          </div>
        </div>
      </div>
      
      <div class="detail-block">
        <div class="block-title">
          <i class="fas fa-clock"></i> 已用时间
        </div>
        <div class="time-indicator">
          <div class="time-progress"></div>
        </div>
        <div class="time-label">已用 3小时 / 预计 5小时</div>
      </div>
      
      <div class="action-buttons">
        <button class="action-btn complete-btn" @click="completeCurrentNode" :disabled="!canCompleteCurrentNode">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M5 13l4 4L19 7"></path>
          </svg>
          完成当前节点
        </button>
        <button class="action-btn timer-btn">
          <i class="fas fa-stopwatch"></i> 开始计时
        </button>
        <button class="action-btn reset-btn" @click="resetNodeStates">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M2.5 2v6h6M21.5 22v-6h-6"></path>
            <path d="M22 11.5A10 10 0 003.2 7.2M2 12.5a10 10 0 0018.8 4.2"></path>
          </svg>
          重置状态
        </button>
      </div>
    </div>
  </div>
  
  <!-- 记录弹窗 -->
  <div class="popup-overlay" v-if="showRecordPopup">
    <div class="popup-content">
      <div class="popup-header">
        <div class="popup-title">记录学习心得</div>
        <button class="close-btn" @click="showRecordPopup = false">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="popup-body">
        <textarea class="note-area" placeholder="记录你的学习心得、遇到的问题或解决方案..."></textarea>
        <div class="photo-option">
          <button class="photo-btn">
            <i class="fas fa-camera photo-icon"></i>
            <span class="photo-label">拍照记录</span>
          </button>
          <button class="photo-btn">
            <i class="fas fa-image photo-icon"></i>
            <span class="photo-label">上传图片</span>
          </button>
        </div>
        <button class="save-btn">保存记录</button>
      </div>
    </div>
  </div>

  <!-- 子任务编辑弹窗 -->
  <div class="popup-overlay" v-if="showSubtaskPopup">
    <div class="popup-content subtask-popup">
      <div class="popup-header">
        <div class="popup-title">编辑子任务 - 关卡 {{ selectedNodeIndex + 1 }}</div>
        <button class="close-btn" @click="showSubtaskPopup = false">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="popup-body">
        <div class="subtask-form">
          <div class="form-group">
            <label>任务名称</label>
            <input type="text" v-model="selectedNode.name" class="form-input" />
          </div>
          <div class="form-group">
            <label>任务描述</label>
            <textarea v-model="selectedNode.description" class="form-textarea"></textarea>
          </div>
          <div class="form-group">
            <label>任务状态</label>
            <select v-model="selectedNode.status" class="form-select">
              <option value="not-started">未开始</option>
              <option value="in-progress">进行中</option>
              <option value="completed">已完成</option>
            </select>
          </div>
        </div>
        <div class="subtask-actions">
          <button class="save-btn" @click="saveSubtask">保存修改</button>
          <button class="cancel-btn" @click="showSubtaskPopup = false">取消</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import gsap from 'gsap'
// SVG画布配置
const canvasWidth = ref(400)
const canvasHeight = ref(180)
const amplitude = ref(60)
const frequency = ref(1.5)

const nodes = ref([
  { id: 1, status: 'completed', name: '基础知识学习', description: '学习React基础概念和语法' },
  { id: 2, status: 'completed', name: '组件开发', description: '创建和使用React组件' },
  { id: 3, status: 'in-progress', name: '状态管理', description: '学习状态管理和生命周期' },
  { id: 4, status: 'not-started', name: '路由配置', description: '配置React Router' },
  { id: 5, status: 'not-started', name: '项目实战', description: '完成综合项目' }
])

const showRecordPopup = ref(false)
const showSubtaskPopup = ref(false)
const selectedNode = ref({})
const selectedNodeIndex = ref(0)

// 完成标准的checkbox状态
const criteria = ref({
  component: false,
  contextApi: false,
  testing: false
})

// SVG相关状态
const pathPoints = ref([])
const nodeStates = ref([])
const currentNodeIndex = ref(0)
const hoverIndex = ref(null)
const clipRightPercent = ref(100)

// 拖动相关状态
const isDragging = ref(false)
const dragStart = ref({ x: 0, y: 0 })
const dragOffset = ref({ x: 0, y: 0 })

// DOM refs
const svgRef = ref(null)
const progressPath = ref(null)

// 节点状态定义
const NODE_STATES = {
  PENDING: 'not-started',
  IN_PROGRESS: 'in-progress',
  COMPLETED: 'completed'
}

// 计算属性
const totalTasks = computed(() => nodes.value.length)
const completedTasks = computed(() => nodes.value.filter(node => node.status === 'completed').length)
const totalTimeSpent = computed(() => '15小时30分钟')
const deadline = computed(() => '2024-01-15')

function openRecordPopup() {
  showRecordPopup.value = true
}

function editSubtask(node, index) {
  selectedNode.value = { ...node }
  selectedNodeIndex.value = index
  showSubtaskPopup.value = true
}

function saveSubtask() {
  nodes.value[selectedNodeIndex.value] = { ...selectedNode.value }
  showSubtaskPopup.value = false
  // 更新节点状态
  updateNodeStates()
}

// 拖动功能实现
function startDrag(event) {
  isDragging.value = true
  const clientX = event.touches ? event.touches[0].clientX : event.clientX
  const clientY = event.touches ? event.touches[0].clientY : event.clientY

  dragStart.value = {
    x: clientX - dragOffset.value.x,
    y: clientY - dragOffset.value.y
  }

  if (event.preventDefault) {
    event.preventDefault()
  }
}

function onDrag(event) {
  if (!isDragging.value) return

  const clientX = event.touches ? event.touches[0].clientX : event.clientX
  const clientY = event.touches ? event.touches[0].clientY : event.clientY

  dragOffset.value = {
    x: clientX - dragStart.value.x,
    y: clientY - dragStart.value.y
  }

  if (event.preventDefault) {
    event.preventDefault()
  }
}

function endDrag() {
  isDragging.value = false
}

// SVG路径生成函数
function generatePathPoints() {
  const points = []
  const centerY = canvasHeight.value / 2
  const nodeCount = nodes.value.length

  // 生成控制点
  const controlPoints = Array.from({ length: nodeCount + 2 }, (_, i) => {
    const x = (i - 1) * (canvasWidth.value / (nodeCount - 1))
    const progress = (i / (nodeCount + 1)) * Math.PI * 2 * frequency.value
    const y = centerY + Math.sin(progress) * amplitude.value
    return [x, y]
  })

  // 生成路径点
  for (let i = 0; i < nodeCount; i++) {
    const t = i / (nodeCount - 1)
    const index = Math.floor(t * (controlPoints.length - 3)) + 1
    const p0 = controlPoints[index - 1] || controlPoints[0]
    const p1 = controlPoints[index] || controlPoints[0]
    const p2 = controlPoints[index + 1] || controlPoints[controlPoints.length - 1]
    const p3 = controlPoints[index + 2] || controlPoints[controlPoints.length - 1]
    const t2 = t * (controlPoints.length - 3) - (index - 1)
    const x = interpolateCatmullRom(p0[0], p1[0], p2[0], p3[0], t2)
    const y = interpolateCatmullRom(p0[1], p1[1], p2[1], p3[1], t2)
    points.push([Math.max(0, Math.min(canvasWidth.value, x)), Math.max(0, Math.min(canvasHeight.value, y))])
  }

  return points
}

function interpolateCatmullRom(p0, p1, p2, p3, t) {
  const v0 = (p2 - p0) * 0.5
  const v1 = (p3 - p1) * 0.5
  const t2 = t * t
  const t3 = t * t2
  return (2 * p1 - 2 * p2 + v0 + v1) * t3 +
         (-3 * p1 + 3 * p2 - 2 * v0 - v1) * t2 +
         v0 * t + p1
}

function pointsToBezierPath(points) {
  if (points.length < 2) return ''

  let d = `M${points[0][0].toFixed(2)},${points[0][1].toFixed(2)}`

  for (let i = 0; i < points.length - 1; i++) {
    const start = points[i]
    const end = points[i + 1]
    const dx = end[0] - start[0]
    const dy = end[1] - start[1]
    const distance = Math.sqrt(dx * dx + dy * dy) * 0.4

    const prevPoint = points[i - 1] || start
    const nextPoint = points[i + 2] || end

    const startAngle = Math.atan2(end[1] - prevPoint[1], end[0] - prevPoint[0])
    const endAngle = Math.atan2(nextPoint[1] - start[1], nextPoint[0] - start[0])

    const ctrl1 = [
      start[0] + Math.cos(startAngle) * distance,
      start[1] + Math.sin(startAngle) * distance
    ]
    const ctrl2 = [
      end[0] - Math.cos(endAngle) * distance,
      end[1] - Math.sin(endAngle) * distance
    ]

    d += ` C${ctrl1[0].toFixed(2)},${ctrl1[1].toFixed(2)},${ctrl2[0].toFixed(2)},${ctrl2[1].toFixed(2)},${end[0].toFixed(2)},${end[1].toFixed(2)}`
  }

  return d
}

const pathD = computed(() => {
  return pointsToBezierPath(pathPoints.value)
})

// 节点状态管理
function updateNodeStates() {
  nodeStates.value = nodes.value.map(node => {
    switch(node.status) {
      case 'completed': return NODE_STATES.COMPLETED
      case 'in-progress': return NODE_STATES.IN_PROGRESS
      case 'not-started': return NODE_STATES.PENDING
      default: return NODE_STATES.PENDING
    }
  })

  // 更新当前节点索引
  const inProgressIndex = nodes.value.findIndex(node => node.status === 'in-progress')
  currentNodeIndex.value = inProgressIndex >= 0 ? inProgressIndex : 0

  // 更新路径动画
  updatePathAnimation()
}

function updatePathAnimation() {
  const completedCount = nodes.value.filter(node => node.status === 'completed').length
  if (completedCount === 0) {
    clipRightPercent.value = 100
    return
  }

  const targetNodeIndex = Math.min(completedCount, pathPoints.value.length - 1)
  if (pathPoints.value[targetNodeIndex]) {
    const targetNodeX = pathPoints.value[targetNodeIndex][0]
    const targetClipPercent = ((canvasWidth.value - targetNodeX) / canvasWidth.value) * 100

    gsap.to(clipRightPercent, {
      value: Math.max(0, targetClipPercent),
      duration: 0.8,
      ease: "power2.out"
    })
  }
}

const getNodeColor = idx => {
  const state = nodeStates.value[idx]
  switch (state) {
    case NODE_STATES.PENDING: return '#ccc'
    case NODE_STATES.IN_PROGRESS: return '#888'
    case NODE_STATES.COMPLETED: return '#555'
    default: return '#ccc'
  }
}

const getNodeRadius = idx => {
  const state = nodeStates.value[idx]
  switch (state) {
    case NODE_STATES.IN_PROGRESS: return 8
    case NODE_STATES.COMPLETED: return 7
    default: return 6
  }
}

// 完成当前节点功能
function animatePathAndNode(segmentIndex, nextNodeIndex) {
  if (!pathPoints.value[nextNodeIndex]) {
    console.error('下一个节点不存在:', nextNodeIndex)
    return
  }

  // 计算目标节点的X坐标百分比
  const nextNodeX = pathPoints.value[nextNodeIndex][0]
  const targetClipPercent = ((canvasWidth.value - nextNodeX) / canvasWidth.value) * 100

  console.log(`动画从节点 ${segmentIndex} 到节点 ${nextNodeIndex}`)
  console.log(`下一个节点X坐标: ${nextNodeX.toFixed(1)}`)
  console.log(`目标裁剪百分比: ${targetClipPercent.toFixed(1)}%`)

  // 使用GSAP动画改变裁剪区域
  gsap.to(clipRightPercent, {
    value: targetClipPercent,
    duration: 1.2,
    ease: "power2.out",
    onComplete: () => {
      nodeStates.value[nextNodeIndex] = NODE_STATES.IN_PROGRESS
      // 同步更新nodes数组
      nodes.value[nextNodeIndex].status = 'in-progress'
      console.log(`动画完成，节点 ${nextNodeIndex} 设为进行中`)
    }
  })
}

function completeCurrentNode() {
  if (currentNodeIndex.value < nodeStates.value.length) {
    const curr = currentNodeIndex.value
    nodeStates.value[curr] = NODE_STATES.COMPLETED
    nodes.value[curr].status = 'completed'

    if (curr < nodeStates.value.length - 1) {
      animatePathAndNode(curr, curr + 1)
      currentNodeIndex.value++
    }
  }
}

function resetNodeStates() {
  // 重置所有节点状态
  nodes.value.forEach((node, index) => {
    if (index === 0) {
      node.status = 'in-progress'
    } else {
      node.status = 'not-started'
    }
  })

  currentNodeIndex.value = 0
  clipRightPercent.value = 100
  updateNodeStates()
}

const canCompleteCurrentNode = computed(() =>
  currentNodeIndex.value < nodeStates.value.length &&
  nodeStates.value[currentNodeIndex.value] === NODE_STATES.IN_PROGRESS &&
  pathPoints.value.length > 0
)

// 初始化路径
async function initializePath() {
  pathPoints.value = generatePathPoints()
  await nextTick()
  updateNodeStates()
}

// 监听节点变化
watch(nodes, () => {
  updateNodeStates()
}, { deep: true })

onMounted(() => {
  setTimeout(() => {
    initializePath()
  }, 100)
})
</script>

<style scoped>
/* 复制你的 style 内容到这里 */
/* ...粘贴全部上面html里的<style>内容... */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
  background-color: #f0f0f0;
  color: #333;
  line-height: 1.6;
  max-width: 500px;
  margin: 0 auto;
  padding: 20px;
}

.app-container {
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  overflow: hidden;
  min-height: 90vh;
}

.header {
  background-color: #f8f8f8;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
}

.progress-overview {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.progress-count {
  font-size: 2rem;
  font-weight: 700;
  color: #222;
}

.progress-count .current {
  color: #555;
}

.progress-count .separator {
  color: #999;
  margin: 0 4px;
}

.progress-count .total {
  color: #999;
}

.progress-label {
  font-size: 0.9rem;
  color: #666;
  margin-top: -4px;
}

.time-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  font-size: 0.85rem;
  color: #666;
}

.total-time {
  font-weight: 600;
  margin-bottom: 2px;
}

.deadline {
  color: #888;
}

.path-board {
  position: relative;
  padding: 20px;
  background-color: #fafafa;
  min-height: 200px;
  overflow: hidden;
  border-radius: 12px;
  margin-bottom: 10px;
}

.svg-container {
  position: relative;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e0e0e0;
  height: 180px;
  width: 100%;
  cursor: grab;
  user-select: none;
}

.svg-container:active {
  cursor: grabbing;
}

.svg-viewport {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: visible;
}

.svg-container svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  transition: transform 0.1s ease-out;
  min-width: 100%;
  min-height: 100%;
}

.node-circle {
  cursor: pointer;
  transition: all 0.3s ease;
}

.node-circle:hover {
  transform: scale(1.2);
}

.node-label {
  font-size: 11px;
  fill: #666;
  font-weight: 600;
  pointer-events: none;
}

.detail-board {
  padding: 20px;
  background-color: #fff;
  border-top: 1px solid #eee;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.detail-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #222;
}

.record-btn {
  background-color: #eee;
  border: none;
  padding: 8px 15px;
  border-radius: 20px;
  color: #555;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: background-color 0.2s ease;
}

.record-btn:hover {
  background-color: #ddd;
}

.detail-block {
  background-color: #f9f9f9;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
}

.block-title {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.block-content {
  background-color: #fff;
  min-height: 80px;
  border-radius: 8px;
  padding: 12px;
  color: #444;
}

.completion-criteria {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  position: relative;
}

.checkbox-item input[type="checkbox"] {
  opacity: 0;
  position: absolute;
  width: 0;
  height: 0;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid #ddd;
  border-radius: 4px;
  margin-right: 12px;
  position: relative;
  transition: all 0.2s ease;
}

.checkbox-item input[type="checkbox"]:checked + .checkmark {
  background-color: #555;
  border-color: #555;
}

.checkbox-item input[type="checkbox"]:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.criteria-text {
  font-size: 0.95rem;
  color: #444;
}

.time-indicator {
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 4px;
  margin-top: 10px;
  position: relative;
  overflow: hidden;
}

.time-progress {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 65%;
  background-color: #888;
  border-radius: 4px;
}

.time-label {
  font-size: 0.8rem;
  color: #777;
  margin-top: 5px;
  text-align: right;
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-top: 20px;
  flex-wrap: wrap;
}

.action-btn {
  flex: 1;
  min-width: 120px;
  height: 45px;
  border-radius: 12px;
  border: none;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.3s ease;
}

.action-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.complete-btn {
  background-color: #555;
  color: white;
}

.complete-btn:hover:not(:disabled) {
  background-color: #444;
}

.timer-btn {
  background-color: #ddd;
  color: #333;
}

.timer-btn:hover {
  background-color: #ccc;
}

.reset-btn {
  background-color: #f44336;
  color: white;
}

.reset-btn:hover {
  background-color: #d32f2f;
}

.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.popup-content {
  background-color: white;
  width: 85%;
  max-width: 400px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.popup-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.popup-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #222;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #777;
  cursor: pointer;
}

.popup-body {
  padding: 20px;
}

.note-area {
  background-color: #f9f9f9;
  border-radius: 10px;
  padding: 15px;
  height: 120px;
  resize: none;
  width: 100%;
  border: 1px solid #eee;
  font-size: 1rem;
  color: #333;
}

.photo-option {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
}

.photo-btn {
  flex: 1;
  background-color: #f0f0f0;
  border: none;
  padding: 15px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.photo-icon {
  font-size: 1.8rem;
  color: #666;
}

.photo-label {
  font-size: 0.9rem;
  color: #555;
}

.save-btn {
  background-color: #555;
  color: white;
  border: none;
  padding: 15px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  margin-top: 20px;
  width: 100%;
  cursor: pointer;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  padding: 10px 20px;
  background-color: #f8f8f8;
  border-top: 1px solid #eee;
  font-size: 0.85rem;
  color: #777;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.completed-dot {
  background-color: #555;
}

.inprogress-dot {
  background-color: #888;
}

.notstarted-dot {
  background-color: #ccc;
}

/* 子任务编辑弹窗样式 */
.subtask-popup .popup-content {
  max-width: 450px;
}

.subtask-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #333;
}

.form-input,
.form-textarea,
.form-select {
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  color: #333;
  background-color: #f9f9f9;
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #555;
  background-color: #fff;
}

.form-textarea {
  min-height: 80px;
  resize: vertical;
}

.subtask-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.cancel-btn {
  flex: 1;
  background-color: #f0f0f0;
  color: #666;
  border: none;
  padding: 15px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.cancel-btn:hover {
  background-color: #e0e0e0;
}
</style>