<template>
  <div class="app-container">
    <div class="header">
      <h1>学习路径进度</h1>
    </div>
    
    <div class="path-board">
      <div class="path-line"></div>
      <div class="nodes-container">
        <div
          v-for="(node, index) in nodes"
          :key="index"
          :class="['node', node.status]"
          @click="openPopup(node)"
        >
          <div class="node-label">关卡 {{ index + 1 }}</div>
        </div>
      </div>
    </div>
    
    <div class="status-bar">
      <div class="status-item">
        <div class="status-dot completed-dot"></div>
        <span>已完成</span>
      </div>
      <div class="status-item">
        <div class="status-dot inprogress-dot"></div>
        <span>进行中</span>
      </div>
      <div class="status-item">
        <div class="status-dot notstarted-dot"></div>
        <span>未开始</span>
      </div>
    </div>
    
    <div class="detail-board">
      <div class="detail-header">
        <div class="detail-title">当前任务详情</div>
        <button class="edit-btn">
          <i class="fas fa-edit"></i> 编辑任务
        </button>
      </div>
      
      <div class="detail-block">
        <div class="block-title">
          <i class="fas fa-tasks"></i> 任务内容
        </div>
        <div class="block-content">
          完成React组件开发实践，包括状态管理和生命周期应用
        </div>
      </div>
      
      <div class="detail-block">
        <div class="block-title">
          <i class="fas fa-flag-checkered"></i> 完成标准
        </div>
        <div class="block-content">
          1. 实现至少3个功能组件<br>
          2. 使用Context API进行状态管理<br>
          3. 通过所有单元测试
        </div>
      </div>
      
      <div class="detail-block">
        <div class="block-title">
          <i class="fas fa-clock"></i> 已用时间
        </div>
        <div class="time-indicator">
          <div class="time-progress"></div>
        </div>
        <div class="time-label">已用 3小时 / 预计 5小时</div>
      </div>
      
      <div class="action-buttons">
        <button class="action-btn complete-btn">
          <i class="fas fa-check-circle"></i> 完成任务
        </button>
        <button class="action-btn timer-btn">
          <i class="fas fa-stopwatch"></i> 开始计时
        </button>
      </div>
    </div>
  </div>
  
  <!-- 弹窗 -->
  <div class="popup-overlay" v-if="showPopup">
    <div class="popup-content">
      <div class="popup-header">
        <div class="popup-title">记录学习心得</div>
        <button class="close-btn" @click="showPopup = false">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="popup-body">
        <textarea class="note-area" placeholder="记录你的学习心得、遇到的问题或解决方案..."></textarea>
        <div class="photo-option">
          <button class="photo-btn">
            <i class="fas fa-camera photo-icon"></i>
            <span class="photo-label">拍照记录</span>
          </button>
          <button class="photo-btn">
            <i class="fas fa-image photo-icon"></i>
            <span class="photo-label">上传图片</span>
          </button>
        </div>
        <button class="save-btn">保存记录</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const nodes = ref([
  { id: 1, status: 'completed' },
  { id: 2, status: 'completed' },
  { id: 3, status: 'in-progress' },
  { id: 4, status: 'not-started' },
  { id: 5, status: 'not-started' }
])

const showPopup = ref(false)

function openPopup(node) {
  if (node.status !== 'not-started') {
    showPopup.value = true
  }
}
</script>

<style scoped>
/* 复制你的 style 内容到这里 */
/* ...粘贴全部上面html里的<style>内容... */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
  background-color: #f0f0f0;
  color: #333;
  line-height: 1.6;
  max-width: 500px;
  margin: 0 auto;
  padding: 20px;
}

.app-container {
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  overflow: hidden;
  min-height: 90vh;
}

.header {
  background-color: #f8f8f8;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
}

.header h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #222;
}

.path-board {
  position: relative;
  padding: 30px 20px;
  background-color: #fafafa;
  min-height: 200px;
  overflow: hidden;
}

.path-line {
  position: absolute;
  top: 50px;
  left: 50px;
  width: calc(100% - 100px);
  height: 10px;
  background-color: #e0e0e0;
  border-radius: 5px;
  z-index: 1;
}

.path-line::after {
  content: '';
  position: absolute;
  top: -20px;
  left: 0;
  width: 100%;
  height: 50px;
  background: linear-gradient(to right, transparent, #fafafa 30%, #fafafa 70%, transparent);
  z-index: 2;
}

.nodes-container {
  position: relative;
  display: flex;
  justify-content: space-between;
  padding: 0 40px;
  z-index: 3;
}

.node {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 4;
}

.node:hover {
  transform: scale(1.1);
}

.node.completed {
  background-color: #555; /* 深灰表示已完成 */
}

.node.in-progress {
  background-color: #888; /* 中灰表示进行中 */
}

.node.not-started {
  background-color: #ccc; /* 浅灰表示未开始 */
}

.node-label {
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.75rem;
  color: #666;
  white-space: nowrap;
}

.detail-board {
  padding: 20px;
  background-color: #fff;
  border-top: 1px solid #eee;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.detail-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #222;
}

.edit-btn {
  background-color: #eee;
  border: none;
  padding: 8px 15px;
  border-radius: 20px;
  color: #555;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
}

.detail-block {
  background-color: #f9f9f9;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
}

.block-title {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.block-content {
  background-color: #fff;
  height: 80px;
  border-radius: 8px;
  padding: 12px;
  color: #444;
}

.time-indicator {
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 4px;
  margin-top: 10px;
  position: relative;
  overflow: hidden;
}

.time-progress {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 65%;
  background-color: #888;
  border-radius: 4px;
}

.time-label {
  font-size: 0.8rem;
  color: #777;
  margin-top: 5px;
  text-align: right;
}

.action-buttons {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

.action-btn {
  flex: 1;
  height: 50px;
  border-radius: 12px;
  border: none;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.complete-btn {
  background-color: #555;
  color: white;
}

.timer-btn {
  background-color: #ddd;
  color: #333;
}

.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.popup-content {
  background-color: white;
  width: 85%;
  max-width: 400px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.popup-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.popup-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #222;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #777;
  cursor: pointer;
}

.popup-body {
  padding: 20px;
}

.note-area {
  background-color: #f9f9f9;
  border-radius: 10px;
  padding: 15px;
  height: 120px;
  resize: none;
  width: 100%;
  border: 1px solid #eee;
  font-size: 1rem;
  color: #333;
}

.photo-option {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
}

.photo-btn {
  flex: 1;
  background-color: #f0f0f0;
  border: none;
  padding: 15px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.photo-icon {
  font-size: 1.8rem;
  color: #666;
}

.photo-label {
  font-size: 0.9rem;
  color: #555;
}

.save-btn {
  background-color: #555;
  color: white;
  border: none;
  padding: 15px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  margin-top: 20px;
  width: 100%;
  cursor: pointer;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  padding: 10px 20px;
  background-color: #f8f8f8;
  border-top: 1px solid #eee;
  font-size: 0.85rem;
  color: #777;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.completed-dot {
  background-color: #555;
}

.inprogress-dot {
  background-color: #888;
}

.notstarted-dot {
  background-color: #ccc;
}
</style>