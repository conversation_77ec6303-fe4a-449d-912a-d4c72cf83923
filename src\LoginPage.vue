<template>
  <div class="login-container">
    <!-- 背景彩色图形层 -->
    <div class="background-shapes">
      <div class="shape shape-1"></div>
      <div class="shape shape-2"></div>
      <div class="shape shape-3"></div>
      <div class="shape shape-4"></div>
    </div>
    
    <!-- 磨砂玻璃内容层 -->
    <div class="frosted-glass">
      <div class="logo-container">
        <div class="logo">LOGO</div>
        <h1 class="system-name">企业管理系统</h1>
      </div>
      
      <h2 class="welcome-text">欢迎回来</h2>
      <p class="sub-text">请选择登录方式</p>
      
      <div class="button-group">
        <button class="login-btn primary">账号密码登录</button>
        <button class="login-btn secondary">手机号快捷登录</button>
      </div>
    </div>
  </div>
</template>

<script setup>
// 这里可以使用Vue3的setup语法
</script>

<style scoped>
/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.login-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-color: #e8f4ff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

/* 背景彩色图形 */
.background-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  filter: blur(30px);
  opacity: 0.7;
}

.shape-1 {
  width: 200px;
  height: 200px;
  background-color: #61a8fe;
  top: 20%;
  left: 10%;
}

.shape-2 {
  width: 250px;
  height: 250px;
  background-color: #ff9a9e;
  bottom: 15%;
  right: 10%;
}

.shape-3 {
  width: 180px;
  height: 180px;
  background-color: #a18cd1;
  top: 50%;
  left: 30%;
}

.shape-4 {
  width: 300px;
  height: 300px;
  background-color: #84fab0;
  bottom: 30%;
  right: 30%;
}

/* 磨砂玻璃效果 */
.frosted-glass {
  position: relative;
  width: 85%;
  max-width: 400px;
  padding: 40px 30px;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
  text-align: center;
  z-index: 10;
}

/* Logo和系统名称 */
.logo-container {
  margin-bottom: 30px;
}

.logo {
  width: 80px;
  height: 80px;
  margin: 0 auto 15px;
  background-color: #61a8fe;
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  font-size: 24px;
  box-shadow: 0 4px 15px rgba(97, 168, 254, 0.3);
}

.system-name {
  color: #333;
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 5px;
}

/* 欢迎文本 */
.welcome-text {
  color: #333;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 10px;
}

.sub-text {
  color: #666;
  font-size: 14px;
  margin-bottom: 30px;
}

/* 按钮样式 */
.button-group {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.login-btn {
  padding: 14px;
  border-radius: 50px;
  border: none;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.login-btn.primary {
  background-color: #61a8fe;
  color: white;
  box-shadow: 0 4px 15px rgba(97, 168, 254, 0.4);
}

.login-btn.primary:hover {
  background-color: #4d9bf7;
  transform: translateY(-2px);
}

.login-btn.secondary {
  background-color: transparent;
  color: #61a8fe;
  border: 1px solid #61a8fe;
}

.login-btn.secondary:hover {
  background-color: rgba(97, 168, 254, 0.1);
  transform: translateY(-2px);
}
</style>